<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LineResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'CODE_LINE' => $this->CODE_LINE,
            'type_service' => $this->type_service,
            'status' => $this->status,
            'commercial_speed' => $this->commercial_speed,
            //'trips' => TripResource::collection($this->whenLoaded('trips')),
            //'line_stations' => LineStationResource::collection($this->whenLoaded('lineStations')),
            'increase' => new IncreaseResource($this->whenLoaded('increase')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}
