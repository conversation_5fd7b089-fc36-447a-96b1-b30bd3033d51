<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class Increase extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;

    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'percentage',
        'date_subscription',
        'date_website',
        'line_id'
    ];

    protected $casts = [
        'percentage' => 'decimal:2',
        'date_subscription' => 'date',
        'date_website' => 'date'
    ];

    /**
     * Get the line that owns the increase.
     */
    public function line(): BelongsTo
    {
        return $this->belongsTo(Line::class, 'line_id');
    }
}
