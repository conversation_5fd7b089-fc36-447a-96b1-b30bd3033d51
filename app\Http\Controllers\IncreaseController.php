<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreIncreaseRequest;
use App\Http\Requests\UpdateIncreaseRequest;
use App\Http\Resources\IncreaseResource;
use App\Models\Increase;
use App\Repositories\IncreaseRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class IncreaseController extends Controller
{
    private IncreaseRepository $repository;

    public function __construct(IncreaseRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(Increase::class, 'increase');
    }

    public function index(): AnonymousResourceCollection
    {
        $increases = $this->repository->with(['line'])->latest()->paginate();
        return IncreaseResource::collection($increases);
    }

    public function all(): AnonymousResourceCollection
    {
        $increases = $this->repository->with(['line'])->all();
        return IncreaseResource::collection($increases);
    }

    public function store(StoreIncreaseRequest $request): JsonResponse
    {
        $increase = $this->repository->create($request->validated());
        $increase->load('line');

        return response()->json([
            'message' => 'Increase created successfully',
            'data' => new IncreaseResource($increase)
        ], 201);
    }

    public function show(Increase $increase): IncreaseResource
    {
        $increase->load('line');
        return new IncreaseResource($increase);
    }

    public function update(UpdateIncreaseRequest $request, Increase $increase): JsonResponse
    {
        $increase = $this->repository->update($request->validated(), $increase->id);
        $increase->load('line');

        return response()->json([
            'message' => 'Increase updated successfully',
            'data' => new IncreaseResource($increase)
        ]);
    }

    public function destroy(Increase $increase): JsonResponse
    {
        $this->repository->delete($increase->id);

        return response()->json([
            'message' => 'Increase deleted successfully'
        ]);
    }
}
