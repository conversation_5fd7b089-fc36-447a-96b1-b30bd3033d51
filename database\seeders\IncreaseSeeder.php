<?php

namespace Database\Seeders;

use App\Models\Increase;
use App\Models\Line;
use Illuminate\Database\Seeder;

class IncreaseSeeder extends Seeder
{
    public function run(): void
    {
        // Get some existing lines to associate with increases
        $lines = Line::take(5)->get();

        if ($lines->isEmpty()) {
            $this->command->info('No lines found. Please seed lines first.');
            return;
        }

        $increases = [
            [
                'nom_fr' => 'Augmentation Été 2025',
                'nom_en' => 'Summer Increase 2025',
                'nom_ar' => 'زيادة صيف 2025',
                'percentage' => 15.50,
                'date_subscription' => '2025-06-01',
                'date_website' => '2025-06-15',
                'line_id' => $lines->get(0)?->id,
            ],
            [
                'nom_fr' => 'Augmentation Carburant',
                'nom_en' => 'Fuel Increase',
                'nom_ar' => 'زيادة الوقود',
                'percentage' => 8.25,
                'date_subscription' => '2025-03-01',
                'date_website' => '2025-03-10',
                'line_id' => $lines->get(1)?->id,
            ],
            [
                'nom_fr' => 'Augmentation Inflation',
                'nom_en' => 'Inflation Increase',
                'nom_ar' => 'زيادة التضخم',
                'percentage' => 12.00,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-15',
                'line_id' => $lines->get(2)?->id,
            ],
            [
                'nom_fr' => 'Augmentation Maintenance',
                'nom_en' => 'Maintenance Increase',
                'nom_ar' => 'زيادة الصيانة',
                'percentage' => 5.75,
                'date_subscription' => '2025-04-01',
                'date_website' => '2025-04-05',
                'line_id' => $lines->get(3)?->id,
            ],
            [
                'nom_fr' => 'Augmentation Spéciale',
                'nom_en' => 'Special Increase',
                'nom_ar' => 'زيادة خاصة',
                'percentage' => 20.00,
                'date_subscription' => '2025-07-01',
                'date_website' => '2025-07-10',
                'line_id' => $lines->get(4)?->id,
            ],
        ];

        foreach ($increases as $increaseData) {
            if ($increaseData['line_id']) {
                Increase::create($increaseData);
            }
        }

        $this->command->info('Increases seeded successfully.');
    }
}
