<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('increases', function (Blueprint $table) {
            $table->id();
            $table->string('nom_fr');
            $table->string('nom_en')->nullable();
            $table->string('nom_ar')->nullable();
            $table->decimal('percentage', 5, 2)->comment('Percentage value between 0-100');
            $table->date('date_subscription');
            $table->date('date_website');
            $table->foreignId('line_id')->constrained('lines')->onDelete('cascade');
            $table->timestamps();

            // Add index for better performance
            $table->index('line_id');
            $table->index('percentage');
            $table->index(['date_subscription', 'date_website']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('increases');
    }
};
