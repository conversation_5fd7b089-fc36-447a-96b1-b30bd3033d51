<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use OwenIt\Auditing\Contracts\Auditable;

class Line extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'CODE_LINE',
        'type_service',
        'status',
        'commercial_speed'
    ];

    protected $casts = [
        'status' => 'boolean',
        'commercial_speed' => 'integer'
    ];

    public function trips(): BelongsToMany
    {
        return $this->belongsToMany(Trip::class, 'line_trip');
    }

    public function lineStations(): HasMany
    {
        return $this->hasMany(LineStation::class, 'id_line');
    }

    public function websiteTrips(): HasMany
    {
        return $this->hasMany(WebsiteTrip::class, 'id_line');
    }

    public function increase(): HasOne
    {
        return $this->hasOne(Increase::class, 'line_id');
    }
}

